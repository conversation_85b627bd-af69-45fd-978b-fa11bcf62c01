from fastmcp import FastMCP
import time
import hashlib
import re
import json
import os
import uuid
import sqlite3
from datetime import datetime
from collections import OrderedDict
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict, field
import logging
import asyncio
from aiohttp import web

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class CacheConfig:
    """缓存配置"""
    max_size: int = 256
    ttl_seconds: int = 3600  # 1小时过期
    enable_persistence: bool = False
    cache_file: str = "mcp_cache.json"

@dataclass
class MemoryConfig:
    """记忆功能配置"""
    enable_memory: bool = True
    db_path: str = "memory.db"
    max_history_per_session: int = 50
    max_sessions: int = 100
    summary_interval: int = 10  # 每10条消息生成一次摘要
    token_limit: int = 2000  # 历史记录的token限制

@dataclass
class OptimizationConfig:
    """优化配置"""
    max_text_length: int = 200
    enable_deduplication: bool = True
    batch_size: int = 10
    similarity_threshold: float = 0.8
    enable_smart_truncation: bool = True

@dataclass
class ServerConfig:
    """服务器配置"""
    default_mode: str = "local"  # "local" 或 "remote"
    host: str = "localhost"
    port: int = 8000
    enable_studio: bool = True

@dataclass
class Message:
    """对话消息"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str = ""
    role: str = ""  # "user", "assistant", "system"
    content: str = ""
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "role": self.role,
            "content": self.content,
            "timestamp": self.timestamp,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """从字典创建消息"""
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            session_id=data.get("session_id", ""),
            role=data.get("role", ""),
            content=data.get("content", ""),
            timestamp=data.get("timestamp", time.time()),
            metadata=data.get("metadata", {})
        )

@dataclass
class Session:
    """对话会话"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    summary: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "metadata": self.metadata,
            "summary": self.summary
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Session':
        """从字典创建会话"""
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            name=data.get("name", ""),
            created_at=data.get("created_at", time.time()),
            updated_at=data.get("updated_at", time.time()),
            metadata=data.get("metadata", {}),
            summary=data.get("summary", "")
        )

class LRUCache:
    """LRU缓存实现，支持TTL"""

    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache = OrderedDict()
        self.timestamps = {}

    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self.timestamps:
            return True
        return time.time() - self.timestamps[key] > self.config.ttl_seconds

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self.cache or self._is_expired(key):
            if key in self.cache:
                del self.cache[key]
                del self.timestamps[key]
            return None

        # 移动到末尾（最近使用）
        value = self.cache.pop(key)
        self.cache[key] = value
        return value

    def put(self, key: str, value: Any) -> None:
        """设置缓存值"""
        if key in self.cache:
            self.cache.pop(key)
        elif len(self.cache) >= self.config.max_size:
            # 移除最旧的项
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            del self.timestamps[oldest_key]

        self.cache[key] = value
        self.timestamps[key] = time.time()

    def clear_expired(self) -> int:
        """清理过期缓存项"""
        expired_keys = [k for k in self.cache.keys() if self._is_expired(k)]
        for key in expired_keys:
            del self.cache[key]
            del self.timestamps[key]
        return len(expired_keys)

class MemoryManager:
    """记忆管理器，负责对话历史的存储和检索"""
    
    def __init__(self, config: MemoryConfig):
        self.config = config
        self.db_path = config.db_path
        self._init_db()
        
    def _init_db(self) -> None:
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建会话表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS sessions (
            id TEXT PRIMARY KEY,
            name TEXT,
            created_at REAL,
            updated_at REAL,
            metadata TEXT,
            summary TEXT
        )
        ''')
        
        # 创建消息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id TEXT PRIMARY KEY,
            session_id TEXT,
            role TEXT,
            content TEXT,
            timestamp REAL,
            metadata TEXT,
            FOREIGN KEY (session_id) REFERENCES sessions (id)
        )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages (session_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages (timestamp)')
        
        conn.commit()
        conn.close()
        
        logger.info(f"记忆数据库初始化完成: {self.db_path}")
    
    def create_session(self, name: str = "", metadata: Dict[str, Any] = None) -> Session:
        """创建新会话"""
        if metadata is None:
            metadata = {}
            
        session = Session(
            name=name or f"会话 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            metadata=metadata
        )
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'INSERT INTO sessions (id, name, created_at, updated_at, metadata, summary) VALUES (?, ?, ?, ?, ?, ?)',
            (session.id, session.name, session.created_at, session.updated_at, json.dumps(session.metadata), session.summary)
        )
        
        conn.commit()
        conn.close()
        
        logger.info(f"创建新会话: {session.id} - {session.name}")
        return session
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """获取会话"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, created_at, updated_at, metadata, summary FROM sessions WHERE id = ?', (session_id,))
        row = cursor.fetchone()
        
        conn.close()
        
        if not row:
            return None
            
        return Session(
            id=row[0],
            name=row[1],
            created_at=row[2],
            updated_at=row[3],
            metadata=json.loads(row[4]) if row[4] else {},
            summary=row[5]
        )
    
    def list_sessions(self, limit: int = 100, offset: int = 0) -> List[Session]:
        """列出会话"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'SELECT id, name, created_at, updated_at, metadata, summary FROM sessions ORDER BY updated_at DESC LIMIT ? OFFSET ?',
            (limit, offset)
        )
        rows = cursor.fetchall()
        
        conn.close()
        
        return [
            Session(
                id=row[0],
                name=row[1],
                created_at=row[2],
                updated_at=row[3],
                metadata=json.loads(row[4]) if row[4] else {},
                summary=row[5]
            )
            for row in rows
        ]
    
    def update_session(self, session: Session) -> None:
        """更新会话"""
        session.updated_at = time.time()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'UPDATE sessions SET name = ?, updated_at = ?, metadata = ?, summary = ? WHERE id = ?',
            (session.name, session.updated_at, json.dumps(session.metadata), session.summary, session.id)
        )
        
        conn.commit()
        conn.close()
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话及其所有消息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 先检查会话是否存在
        cursor.execute('SELECT id FROM sessions WHERE id = ?', (session_id,))
        if not cursor.fetchone():
            conn.close()
            return False
        
        # 删除会话的所有消息
        cursor.execute('DELETE FROM messages WHERE session_id = ?', (session_id,))
        
        # 删除会话
        cursor.execute('DELETE FROM sessions WHERE id = ?', (session_id,))
        
        conn.commit()
        conn.close()
        
        logger.info(f"删除会话: {session_id}")
        return True
    
    def add_message(self, message: Message) -> None:
        """添加消息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'INSERT INTO messages (id, session_id, role, content, timestamp, metadata) VALUES (?, ?, ?, ?, ?, ?)',
            (message.id, message.session_id, message.role, message.content, message.timestamp, json.dumps(message.metadata))
        )
        
        # 更新会话的更新时间
        cursor.execute('UPDATE sessions SET updated_at = ? WHERE id = ?', (time.time(), message.session_id))
        
        conn.commit()
        conn.close()
        
        # 检查是否需要生成摘要
        self._check_and_generate_summary(message.session_id)
    
    def get_messages(self, session_id: str, limit: int = 50, offset: int = 0) -> List[Message]:
        """获取会话的消息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'SELECT id, session_id, role, content, timestamp, metadata FROM messages WHERE session_id = ? ORDER BY timestamp ASC LIMIT ? OFFSET ?',
            (session_id, limit, offset)
        )
        rows = cursor.fetchall()
        
        conn.close()
        
        return [
            Message(
                id=row[0],
                session_id=row[1],
                role=row[2],
                content=row[3],
                timestamp=row[4],
                metadata=json.loads(row[5]) if row[5] else {}
            )
            for row in rows
        ]
    
    def get_conversation_history(self, session_id: str, token_limit: int = None) -> List[Dict[str, Any]]:
        """获取对话历史，格式化为适合LLM的格式"""
        if token_limit is None:
            token_limit = self.config.token_limit
            
        messages = self.get_messages(session_id)
        
        # 简单的token计数估算（每个英文单词约为1个token，每个中文字符约为1.5个token）
        def estimate_tokens(text: str) -> int:
            # 英文单词数（粗略估计）
            english_words = len(re.findall(r'[a-zA-Z]+', text))
            # 中文字符数
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
            # 其他字符数
            other_chars = len(text) - english_words - chinese_chars
            
            return english_words + int(chinese_chars * 1.5) + other_chars
        
        # 格式化消息
        formatted_messages = [
            {"role": msg.role, "content": msg.content}
            for msg in messages
        ]
        
        # 如果超出token限制，进行裁剪
        if token_limit > 0:
            total_tokens = sum(estimate_tokens(msg["content"]) for msg in formatted_messages)
            
            if total_tokens > token_limit:
                # 获取会话摘要
                session = self.get_session(session_id)
                summary = session.summary if session else ""
                
                if summary:
                    # 使用摘要替代早期消息
                    summary_msg = {"role": "system", "content": f"以下是之前对话的摘要：{summary}"}
                    
                    # 保留最近的消息，但确保不超过token限制
                    current_tokens = estimate_tokens(summary_msg["content"])
                    recent_messages = []
                    
                    for msg in reversed(formatted_messages):
                        msg_tokens = estimate_tokens(msg["content"])
                        if current_tokens + msg_tokens <= token_limit:
                            recent_messages.insert(0, msg)
                            current_tokens += msg_tokens
                        else:
                            break
                    
                    return [summary_msg] + recent_messages
                else:
                    # 没有摘要，只保留最近的消息
                    current_tokens = 0
                    recent_messages = []
                    
                    for msg in reversed(formatted_messages):
                        msg_tokens = estimate_tokens(msg["content"])
                        if current_tokens + msg_tokens <= token_limit:
                            recent_messages.insert(0, msg)
                            current_tokens += msg_tokens
                        else:
                            break
                    
                    return recent_messages
        
        return formatted_messages
    
    def _check_and_generate_summary(self, session_id: str) -> None:
        """检查是否需要生成摘要，并生成"""
        if not self.config.enable_memory:
            return
            
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取会话消息数量
        cursor.execute('SELECT COUNT(*) FROM messages WHERE session_id = ?', (session_id,))
        count = cursor.fetchone()[0]
        
        conn.close()
        
        # 如果消息数量是摘要间隔的倍数，生成摘要
        if count > 0 and count % self.config.summary_interval == 0:
            self._generate_summary(session_id)
    
    def _generate_summary(self, session_id: str) -> None:
        """生成会话摘要"""
        # 获取会话消息
        messages = self.get_messages(session_id)
        
        if not messages:
            return
            
        # 这里应该调用LLM生成摘要，但为了简化，我们只是拼接最近几条消息
        # 在实际应用中，应该调用LLM API生成更有意义的摘要
        recent_messages = messages[-min(10, len(messages)):]
        summary_text = "对话摘要: " + " ".join([f"({msg.role}){msg.content[:30]}..." for msg in recent_messages])
        
        # 更新会话摘要
        session = self.get_session(session_id)
        if session:
            session.summary = summary_text
            self.update_session(session)
            logger.info(f"为会话 {session_id} 生成摘要")

class TextOptimizer:
    """文本优化器"""

    def __init__(self, config: OptimizationConfig):
        self.config = config

    def smart_truncate(self, text: str) -> str:
        """智能截断文本，保留重要信息"""
        if len(text) <= self.config.max_text_length:
            return text

        if not self.config.enable_smart_truncation:
            return text[:self.config.max_text_length] + "..."

        # 尝试在句子边界截断
        sentences = re.split(r'[.!?。！？]', text)
        result = ""
        for sentence in sentences:
            if len(result + sentence) <= self.config.max_text_length - 3:
                result += sentence + "."
            else:
                break

        if not result:
            # 如果没有找到合适的句子边界，在单词边界截断
            words = text.split()
            result = ""
            for word in words:
                if len(result + word) <= self.config.max_text_length - 3:
                    result += word + " "
                else:
                    break
            result = result.strip()

        return result + "..." if result else text[:self.config.max_text_length] + "..."

    def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        # 简单的基于字符集合的相似度计算
        set1 = set(text1.lower().split())
        set2 = set(text2.lower().split())

        if not set1 and not set2:
            return 1.0
        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0.0

    def generate_hash(self, text: str) -> str:
        """生成文本哈希"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

class OptimizedMCPServer:
    """优化的MCP服务器，支持记忆功能和多种传输方式"""

    def __init__(self,
                 cache_config: Optional[CacheConfig] = None,
                 optimization_config: Optional[OptimizationConfig] = None,
                 memory_config: Optional[MemoryConfig] = None,
                 server_config: Optional[ServerConfig] = None):
        self.cache_config = cache_config or CacheConfig()
        self.optimization_config = optimization_config or OptimizationConfig()
        self.memory_config = memory_config or MemoryConfig()
        self.server_config = server_config or ServerConfig()

        self.cache = LRUCache(self.cache_config)
        self.optimizer = TextOptimizer(self.optimization_config)
        self.memory = MemoryManager(self.memory_config) if self.memory_config.enable_memory else None
        self.mcp = FastMCP("OptimizedMCPServer")

        # 请求去重存储
        self.recent_requests = OrderedDict()
        self.request_history_size = 100
        
        # 当前活跃会话
        self.active_sessions = {}  # client_id -> session_id
        
        # SSE客户端
        self.sse_clients = {}  # client_id -> response

        self._register_tools()

        logger.info(f"MCP服务器初始化完成，缓存大小: {self.cache_config.max_size}，记忆功能: {'启用' if self.memory else '禁用'}")

    def _register_tools(self):
        """注册工具函数"""

        @self.mcp.tool()
        def process_text(text: str, client_id: str = "", remember: bool = False) -> Dict[str, Any]:
            """处理单个文本，优化token使用
            
            Args:
                text: 要处理的文本
                client_id: 客户端ID，用于关联会话
                remember: 是否记住这条消息
            """
            result = self._process_single_text(text)
            
            # 如果启用记忆功能且需要记住这条消息
            if self.memory and remember and client_id:
                session_id = self._get_or_create_session(client_id)
                
                # 添加用户消息
                self.memory.add_message(Message(
                    session_id=session_id,
                    role="user",
                    content=text
                ))
                
                # 添加助手回复
                self.memory.add_message(Message(
                    session_id=session_id,
                    role="assistant",
                    content=result["result"]
                ))
                
                result["session_id"] = session_id
            
            return result

        @self.mcp.tool()
        def process_batch(texts: List[str], client_id: str = "", remember: bool = False) -> Dict[str, Any]:
            """批量处理文本，减少请求次数
            
            Args:
                texts: 要处理的文本列表
                client_id: 客户端ID，用于关联会话
                remember: 是否记住这些消息
            """
            result = self._process_batch_texts(texts)
            
            # 如果启用记忆功能且需要记住这些消息
            if self.memory and remember and client_id:
                session_id = self._get_or_create_session(client_id)
                
                for i, item in enumerate(result["results"]):
                    if item.get("success"):
                        # 添加用户消息
                        self.memory.add_message(Message(
                            session_id=session_id,
                            role="user",
                            content=texts[i]
                        ))
                        
                        # 添加助手回复
                        self.memory.add_message(Message(
                            session_id=session_id,
                            role="assistant",
                            content=item["result"]
                        ))
                
                result["session_id"] = session_id
            
            return result

        @self.mcp.tool()
        def get_cache_stats() -> Dict[str, Any]:
            """获取缓存统计信息"""
            return self._get_cache_statistics()

        @self.mcp.tool()
        def clear_cache() -> Dict[str, str]:
            """清理缓存"""
            return self._clear_cache()

        @self.mcp.tool()
        def optimize_config(
            max_text_length: Optional[int] = None,
            enable_deduplication: Optional[bool] = None,
            batch_size: Optional[int] = None
        ) -> Dict[str, Any]:
            """动态调整优化配置"""
            return self._update_optimization_config(
                max_text_length, enable_deduplication, batch_size
            )
            
        # 记忆功能相关工具
        if self.memory:
            @self.mcp.tool()
            def create_session(name: str = "", client_id: str = "") -> Dict[str, Any]:
                """创建新的对话会话
                
                Args:
                    name: 会话名称
                    client_id: 客户端ID，用于关联会话
                """
                session = self.memory.create_session(name)
                
                if client_id:
                    self.active_sessions[client_id] = session.id
                
                return {
                    "status": "success",
                    "session": session.to_dict()
                }
            
            @self.mcp.tool()
            def get_session(session_id: str) -> Dict[str, Any]:
                """获取会话信息
                
                Args:
                    session_id: 会话ID
                """
                session = self.memory.get_session(session_id)
                
                if not session:
                    return {
                        "status": "error",
                        "message": f"会话不存在: {session_id}"
                    }
                
                return {
                    "status": "success",
                    "session": session.to_dict()
                }
            
            @self.mcp.tool()
            def list_sessions(limit: int = 100, offset: int = 0) -> Dict[str, Any]:
                """列出会话
                
                Args:
                    limit: 返回的最大会话数
                    offset: 分页偏移量
                """
                sessions = self.memory.list_sessions(limit, offset)
                
                return {
                    "status": "success",
                    "sessions": [session.to_dict() for session in sessions],
                    "total": len(sessions),
                    "limit": limit,
                    "offset": offset
                }
            
            @self.mcp.tool()
            def delete_session(session_id: str) -> Dict[str, Any]:
                """删除会话
                
                Args:
                    session_id: 会话ID
                """
                success = self.memory.delete_session(session_id)
                
                if not success:
                    return {
                        "status": "error",
                        "message": f"会话不存在或删除失败: {session_id}"
                    }
                
                # 清理活跃会话映射
                for client_id, sid in list(self.active_sessions.items()):
                    if sid == session_id:
                        del self.active_sessions[client_id]
                
                return {
                    "status": "success",
                    "message": f"会话已删除: {session_id}"
                }
            
            @self.mcp.tool()
            def get_conversation_history(session_id: str = "", client_id: str = "", token_limit: int = None) -> Dict[str, Any]:
                """获取对话历史
                
                Args:
                    session_id: 会话ID
                    client_id: 客户端ID，如果提供则使用关联的会话
                    token_limit: token限制
                """
                # 确定会话ID
                if not session_id and client_id:
                    session_id = self.active_sessions.get(client_id)
                
                if not session_id:
                    return {
                        "status": "error",
                        "message": "未提供有效的会话ID"
                    }
                
                # 获取会话
                session = self.memory.get_session(session_id)
                if not session:
                    return {
                        "status": "error",
                        "message": f"会话不存在: {session_id}"
                    }
                
                # 获取对话历史
                history = self.memory.get_conversation_history(session_id, token_limit)
                
                return {
                    "status": "success",
                    "session": session.to_dict(),
                    "history": history,
                    "message_count": len(history)
                }
            
            @self.mcp.tool()
            def add_message(session_id: str, role: str, content: str, client_id: str = "") -> Dict[str, Any]:
                """添加消息到会话
                
                Args:
                    session_id: 会话ID
                    role: 角色 (user, assistant, system)
                    content: 消息内容
                    client_id: 客户端ID，如果提供则使用关联的会话
                """
                # 确定会话ID
                if not session_id and client_id:
                    session_id = self.active_sessions.get(client_id)
                    if not session_id:
                        session_id = self._get_or_create_session(client_id)
                
                if not session_id:
                    return {
                        "status": "error",
                        "message": "未提供有效的会话ID"
                    }
                
                # 验证角色
                if role not in ["user", "assistant", "system"]:
                    return {
                        "status": "error",
                        "message": f"无效的角色: {role}，必须是 user, assistant 或 system"
                    }
                
                # 添加消息
                message = Message(
                    session_id=session_id,
                    role=role,
                    content=content
                )
                self.memory.add_message(message)
                
                return {
                    "status": "success",
                    "message": message.to_dict(),
                    "session_id": session_id
                }

    def _process_single_text(self, text: str) -> Dict[str, Any]:
        """处理单个文本"""
        start_time = time.time()

        # 生成缓存键
        cache_key = self.optimizer.generate_hash(text)

        # 检查缓存
        cached_result = self.cache.get(cache_key)
        if cached_result:
            logger.info(f"缓存命中: {cache_key[:8]}...")
            return {
                "result": cached_result,
                "cached": True,
                "processing_time": time.time() - start_time,
                "original_length": len(text),
                "optimized_length": len(cached_result)
            }

        # 检查去重
        if self.optimization_config.enable_deduplication:
            similar_result = self._find_similar_request(text)
            if similar_result:
                logger.info(f"找到相似请求，复用结果")
                self.cache.put(cache_key, similar_result)
                return {
                    "result": similar_result,
                    "cached": False,
                    "deduplicated": True,
                    "processing_time": time.time() - start_time,
                    "original_length": len(text),
                    "optimized_length": len(similar_result)
                }

        # 优化文本
        optimized_text = self.optimizer.smart_truncate(text)

        # 模拟处理（实际应用中这里会调用LLM或其他处理逻辑）
        result = f"[{int(time.time())}] 处理结果: {optimized_text}"

        # 存储到缓存
        self.cache.put(cache_key, result)

        # 更新请求历史
        self._update_request_history(text, result)

        processing_time = time.time() - start_time
        logger.info(f"处理完成，耗时: {processing_time:.3f}s")

        return {
            "result": result,
            "cached": False,
            "deduplicated": False,
            "processing_time": processing_time,
            "original_length": len(text),
            "optimized_length": len(optimized_text),
            "compression_ratio": len(optimized_text) / len(text) if len(text) > 0 else 1.0
        }

    def _process_batch_texts(self, texts: List[str]) -> Dict[str, Any]:
        """批量处理文本"""
        start_time = time.time()
        results = []
        cache_hits = 0
        deduplication_hits = 0

        # 限制批处理大小
        if len(texts) > self.optimization_config.batch_size:
            logger.warning(f"批处理大小超限，截断到 {self.optimization_config.batch_size}")
            texts = texts[:self.optimization_config.batch_size]

        for i, text in enumerate(texts):
            try:
                result = self._process_single_text(text)
                results.append({
                    "index": i,
                    "success": True,
                    **result
                })

                if result.get("cached"):
                    cache_hits += 1
                if result.get("deduplicated"):
                    deduplication_hits += 1

            except Exception as e:
                logger.error(f"处理文本 {i} 时出错: {str(e)}")
                results.append({
                    "index": i,
                    "success": False,
                    "error": str(e)
                })

        total_time = time.time() - start_time

        return {
            "results": results,
            "summary": {
                "total_texts": len(texts),
                "successful": sum(1 for r in results if r.get("success")),
                "cache_hits": cache_hits,
                "deduplication_hits": deduplication_hits,
                "total_processing_time": total_time,
                "average_time_per_text": total_time / len(texts) if texts else 0
            }
        }

    def _find_similar_request(self, text: str) -> Optional[str]:
        """查找相似的历史请求"""
        for request_text, result in self.recent_requests.items():
            similarity = self.optimizer.calculate_similarity(text, request_text)
            if similarity >= self.optimization_config.similarity_threshold:
                logger.info(f"找到相似请求，相似度: {similarity:.2f}")
                return result
        return None

    def _update_request_history(self, text: str, result: str) -> None:
        """更新请求历史"""
        if len(self.recent_requests) >= self.request_history_size:
            # 移除最旧的请求
            self.recent_requests.popitem(last=False)

        self.recent_requests[text] = result

    def _get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        expired_count = self.cache.clear_expired()

        return {
            "cache_size": len(self.cache.cache),
            "max_cache_size": self.cache_config.max_size,
            "cache_usage_ratio": len(self.cache.cache) / self.cache_config.max_size,
            "expired_items_cleared": expired_count,
            "request_history_size": len(self.recent_requests),
            "ttl_seconds": self.cache_config.ttl_seconds,
            "optimization_config": asdict(self.optimization_config)
        }

    def _clear_cache(self) -> Dict[str, str]:
        """清理缓存"""
        cache_size = len(self.cache.cache)
        history_size = len(self.recent_requests)

        self.cache.cache.clear()
        self.cache.timestamps.clear()
        self.recent_requests.clear()

        logger.info(f"缓存已清理，移除了 {cache_size} 个缓存项和 {history_size} 个历史记录")

        return {
            "status": "success",
            "message": f"已清理 {cache_size} 个缓存项和 {history_size} 个历史记录"
        }

    def _update_optimization_config(self,
                                  max_text_length: Optional[int] = None,
                                  enable_deduplication: Optional[bool] = None,
                                  batch_size: Optional[int] = None) -> Dict[str, Any]:
        """更新优化配置"""
        old_config = asdict(self.optimization_config)

        if max_text_length is not None:
            self.optimization_config.max_text_length = max_text_length
        if enable_deduplication is not None:
            self.optimization_config.enable_deduplication = enable_deduplication
        if batch_size is not None:
            self.optimization_config.batch_size = batch_size

        new_config = asdict(self.optimization_config)

        logger.info("优化配置已更新")

        return {
            "status": "success",
            "old_config": old_config,
            "new_config": new_config
        }

    def _get_or_create_session(self, client_id: str) -> str:
        """获取或创建会话ID"""
        if not self.memory:
            return ""
            
        # 检查是否已有关联会话
        session_id = self.active_sessions.get(client_id)
        if session_id:
            # 验证会话是否存在
            session = self.memory.get_session(session_id)
            if session:
                return session_id
        
        # 创建新会话
        session = self.memory.create_session(f"客户端 {client_id} 的会话")
        self.active_sessions[client_id] = session.id
        logger.info(f"为客户端 {client_id} 创建新会话: {session.id}")
        
        return session.id
    
    async def _sse_handler(self, request):
        """SSE请求处理器"""
        client_id = request.query.get('client_id', str(uuid.uuid4()))
        logger.info(f"新的SSE连接: {client_id}")
        
        # 创建响应
        response = web.StreamResponse()
        response.headers['Content-Type'] = 'text/event-stream'
        response.headers['Cache-Control'] = 'no-cache'
        response.headers['Connection'] = 'keep-alive'
        await response.prepare(request)
        
        # 添加到客户端列表
        self.sse_clients[client_id] = response
        
        try:
            # 发送连接成功消息
            await response.write(f"data: {json.dumps({'event': 'connected', 'client_id': client_id})}\n\n".encode('utf-8'))
            
            # 保持连接活跃
            while True:
                await asyncio.sleep(30)
                await response.write(f"data: {json.dumps({'event': 'ping'})}\n\n".encode('utf-8'))
        except ConnectionResetError:
            logger.info(f"SSE客户端断开连接: {client_id}")
        finally:
            # 从客户端列表移除
            if client_id in self.sse_clients:
                del self.sse_clients[client_id]
        
        return response
        
    async def _handle_list_sessions(self, request):
        """处理获取会话列表请求"""
        if not self.memory:
            return web.json_response({"status": "error", "message": "记忆功能未启用"})
        
        try:
            limit = int(request.query.get('limit', 100))
            offset = int(request.query.get('offset', 0))
            
            sessions = self.memory.list_sessions(limit, offset)
            
            return web.json_response({
                "status": "success",
                "sessions": [session.to_dict() for session in sessions],
                "total": len(sessions),
                "limit": limit,
                "offset": offset
            })
        except Exception as e:
            logger.error(f"获取会话列表出错: {str(e)}")
            return web.json_response({"status": "error", "message": str(e)})
    
    async def _handle_create_session(self, request):
        """处理创建会话请求"""
        if not self.memory:
            return web.json_response({"status": "error", "message": "记忆功能未启用"})
        
        try:
            data = await request.json()
            name = data.get('name', '')
            client_id = data.get('client_id', '')
            
            session = self.memory.create_session(name)
            
            if client_id:
                self.active_sessions[client_id] = session.id
            
            return web.json_response({
                "status": "success",
                "session": session.to_dict()
            })
        except Exception as e:
            logger.error(f"创建会话出错: {str(e)}")
            return web.json_response({"status": "error", "message": str(e)})
    
    async def _handle_get_session(self, request):
        """处理获取会话信息请求"""
        if not self.memory:
            return web.json_response({"status": "error", "message": "记忆功能未启用"})
        
        try:
            session_id = request.match_info['session_id']
            session = self.memory.get_session(session_id)
            
            if not session:
                return web.json_response({
                    "status": "error",
                    "message": f"会话不存在: {session_id}"
                })
            
            return web.json_response({
                "status": "success",
                "session": session.to_dict()
            })
        except Exception as e:
            logger.error(f"获取会话信息出��: {str(e)}")
            return web.json_response({"status": "error", "message": str(e)})
    
    async def _handle_delete_session(self, request):
        """处理删除会话请求"""
        if not self.memory:
            return web.json_response({"status": "error", "message": "记忆功能未启用"})
        
        try:
            session_id = request.match_info['session_id']
            success = self.memory.delete_session(session_id)
            
            if not success:
                return web.json_response({
                    "status": "error",
                    "message": f"会话不存在或删除失败: {session_id}"
                })
            
            # 清理活跃会话映射
            for client_id, sid in list(self.active_sessions.items()):
                if sid == session_id:
                    del self.active_sessions[client_id]
            
            return web.json_response({
                "status": "success",
                "message": f"会话已删除: {session_id}"
            })
        except Exception as e:
            logger.error(f"删除会话出错: {str(e)}")
            return web.json_response({"status": "error", "message": str(e)})
    
    async def _handle_get_messages(self, request):
        """处理获取���话消息请求"""
        if not self.memory:
            return web.json_response({"status": "error", "message": "记忆功能未启用"})
        
        try:
            session_id = request.match_info['session_id']
            limit = int(request.query.get('limit', 50))
            offset = int(request.query.get('offset', 0))
            
            messages = self.memory.get_messages(session_id, limit, offset)
            
            return web.json_response({
                "status": "success",
                "messages": [msg.to_dict() for msg in messages],
                "total": len(messages),
                "limit": limit,
                "offset": offset
            })
        except Exception as e:
            logger.error(f"获取会话消息出错: {str(e)}")
            return web.json_response({"status": "error", "message": str(e)})
    
    async def _handle_process_text(self, request):
        """处理文本处理请求"""
        try:
            data = await request.json()
            text = data.get('text', '')
            client_id = data.get('client_id', '')
            remember = data.get('remember', False)
            session_id = data.get('session_id', '')
            
            if not text:
                return web.json_response({
                    "status": "error",
                    "message": "未提供文本内容"
                })
            
            # 处理文本
            result = self._process_single_text(text)
            
            # 如果启用记忆功能且需要记住这条消息
            if self.memory and remember:
                if not session_id and client_id:
                    session_id = self._get_or_create_session(client_id)
                
                if session_id:
                    # 添加用户消息
                    self.memory.add_message(Message(
                        session_id=session_id,
                        role="user",
                        content=text
                    ))
                    
                    # 添加助手回复
                    self.memory.add_message(Message(
                        session_id=session_id,
                        role="assistant",
                        content=result["result"]
                    ))
                    
                    result["session_id"] = session_id
            
            return web.json_response(result)
        except Exception as e:
            logger.error(f"处理文本出错: {str(e)}")
            return web.json_response({"status": "error", "message": str(e)})
    
    async def _send_sse_event(self, client_id: str, event: str, data: Dict[str, Any]):
        """向SSE客户端发送事件"""
        if client_id not in self.sse_clients:
            logger.warning(f"尝试向不存在的SSE客户端发送事件: {client_id}")
            return False
            
        message = {
            "event": event,
            "data": data
        }
        
        try:
            response = self.sse_clients[client_id]
            await response.write(f"data: {json.dumps(message)}\n\n".encode('utf-8'))
            logger.info(f"向客户端 {client_id} 发送SSE事件: {event}")
            return True
        except Exception as e:
            logger.error(f"向客户端 {client_id} 发送SSE事件失败: {str(e)}")
            return False
    
    async def _handle_root(self, request):
        """处理根路径请求，重定向到Studio界面"""
        raise web.HTTPFound('/studio')
        
    async def _start_sse_server(self, host: str = "localhost", port: int = 8000):
        """启动SSE服务器"""
        app = web.Application()
        
        # 添加根路径重定向
        app.router.add_get('/', self._handle_root)
        
        # 添加SSE路由
        app.router.add_get('/events', self._sse_handler)
        
        # 添加API路由
        app.router.add_get('/api/sessions', self._handle_list_sessions)
        app.router.add_post('/api/sessions', self._handle_create_session)
        app.router.add_get('/api/sessions/{session_id}', self._handle_get_session)
        app.router.add_delete('/api/sessions/{session_id}', self._handle_delete_session)
        app.router.add_get('/api/sessions/{session_id}/messages', self._handle_get_messages)
        app.router.add_post('/api/process', self._handle_process_text)
        
        # 添加静态文件服务
        import os
        studio_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'studio')
        
        # 检查studio目录是否存在
        if not os.path.exists(studio_path):
            logger.warning(f"Studio目录不存在: {studio_path}，创建目录")
            os.makedirs(studio_path, exist_ok=True)
            
        # 添加静态文件路由
        app.router.add_static('/studio/', path=studio_path, name='studio')
        
        # 添加studio首页路由
        async def handle_studio(request):
            index_path = os.path.join(studio_path, 'index.html')
            if os.path.exists(index_path):
                with open(index_path, 'rb') as f:
                    return web.Response(body=f.read(), content_type='text/html')
            else:
                return web.Response(text="Studio界面未安装", content_type='text/html')
                
        app.router.add_get('/studio', handle_studio)
        
        # 启动服务器
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()
        
        logger.info(f"SSE服务器已启动: http://{host}:{port}")
        logger.info(f"Studio界面: http://{host}:{port}/studio")
        
        # 保持服务器运行
        while True:
            await asyncio.sleep(3600)  # 每小时检查一次
    
    def run(self, transport: str = None, host: str = None, port: int = None):
        """启动服务器

        Args:
            transport: 传输方式，支持 "stdio"、"sse" 或 "both"，如果为None则使用配置中的默认值
            host: SSE服务器主机名，如果为None则使用配置中的默认值
            port: SSE服务器端口，如果为None则使用配置中的默认值
        """
        # 如果未指定参数，使用配置中的默认值
        if transport is None:
            transport = "stdio" if self.server_config.default_mode == "local" else "sse"
        
        actual_host = host if host is not None else self.server_config.host
        actual_port = port if port is not None else self.server_config.port
        
        # 根据配置决定是否使用远程模式（0.0.0.0）
        if self.server_config.default_mode == "remote" and actual_host == "localhost":
            actual_host = "0.0.0.0"
            logger.info(f"使用远程模式，监听所有网络接口")
        
        if transport == "stdio":
            logger.info("启动优化的MCP服务器，使用stdio传输")
            self.mcp.run()
        elif transport == "sse":
            logger.info(f"启动优化的MCP服务器，使用SSE传输，地址: http://{actual_host}:{actual_port}")
            asyncio.run(self._start_sse_server(actual_host, actual_port))
        elif transport == "both":
            logger.info(f"启动优化的MCP服务器，同时使用stdio和SSE传输，SSE地址: http://{actual_host}:{actual_port}")
            # 在单独的线程中启动SSE服务器
            import threading
            sse_thread = threading.Thread(
                target=lambda: asyncio.run(self._start_sse_server(actual_host, actual_port)),
                daemon=True
            )
            sse_thread.start()
            
            # 主线程运行stdio服务
            self.mcp.run()
        else:
            raise ValueError(f"不支持的传输方式: {transport}，支持的选项: stdio, sse, both")

    def run_stdio(self):
        """使用stdio传输启动服务器"""
        logger.info("启动优化的MCP服务器，使用stdio传输")
        self.mcp.run()
    
    def run_sse(self, host: str = None, port: int = None):
        """使用SSE传输启动服务器"""
        actual_host = host if host is not None else self.server_config.host
        actual_port = port if port is not None else self.server_config.port
        
        # 根据配置决定是否使用远程模式（0.0.0.0）
        if self.server_config.default_mode == "remote" and actual_host == "localhost":
            actual_host = "0.0.0.0"
            logger.info(f"使用远程模式，监听所有网络接口")
            
        logger.info(f"启动优化的MCP服务器，使用SSE传输，地址: http://{actual_host}:{actual_port}")
        asyncio.run(self._start_sse_server(actual_host, actual_port))
    
    def run_studio(self, host: str = None, port: int = None):
        """启动本地Studio服务器"""
        actual_host = host if host is not None else self.server_config.host
        actual_port = port if port is not None else self.server_config.port
        
        # 根据配置决定是否使用远程模式（0.0.0.0）
        if self.server_config.default_mode == "remote" and actual_host == "localhost":
            actual_host = "0.0.0.0"
            logger.info(f"使用远程模式，监听所有网络接口")
            
        logger.info(f"启动{'远程' if actual_host == '0.0.0.0' else '本地'}Studio服务器，地址: http://{actual_host}:{actual_port}/studio")
        self.run(transport="sse", host=actual_host, port=actual_port)

    def get_mcp_instance(self):
        """获取FastMCP实例，用于自定义启动"""
        return self.mcp

# 创建服务器实例
def create_server(cache_size: int = 256,
                 max_text_length: int = 200,
                 enable_deduplication: bool = True,
                 enable_memory: bool = True,
                 db_path: str = "memory.db",
                 default_mode: str = "local",
                 host: str = "localhost",
                 port: int = 8000) -> OptimizedMCPServer:
    """创建优化的MCP服务器实例
    
    Args:
        cache_size: 缓存大小
        max_text_length: 最大文本长度
        enable_deduplication: 是否启用去重
        enable_memory: 是否启用记忆功能
        db_path: 记忆数据库路径
        default_mode: 默认运行模式，"local"（本地）或"remote"（远程）
        host: 服务器主机名
        port: 服务器端口
    
    Returns:
        OptimizedMCPServer: 服务器实例
    """
    cache_config = CacheConfig(max_size=cache_size)
    optimization_config = OptimizationConfig(
        max_text_length=max_text_length,
        enable_deduplication=enable_deduplication
    )
    memory_config = MemoryConfig(
        enable_memory=enable_memory,
        db_path=db_path
    )
    server_config = ServerConfig(
        default_mode=default_mode,
        host=host,
        port=port
    )

    return OptimizedMCPServer(cache_config, optimization_config, memory_config, server_config)

def create_studio_server(host: str = "localhost", 
                        port: int = 8000,
                        cache_size: int = 256,
                        enable_memory: bool = True,
                        default_mode: str = "local") -> OptimizedMCPServer:
    """创建并启动Studio服务器
    
    Args:
        host: 主机名
        port: 端口
        cache_size: 缓存大小
        enable_memory: 是否启用记忆功能
        default_mode: 默认运行模式，"local"（本地）或"remote"（远程）
    
    Returns:
        OptimizedMCPServer: 服务器实例
    """
    server = create_server(
        cache_size=cache_size,
        enable_memory=enable_memory,
        default_mode=default_mode,
        host=host,
        port=port
    )
    
    # 在新线程中启动服务器
    import threading
    server_thread = threading.Thread(
        target=lambda: server.run_studio(),
        daemon=True
    )
    server_thread.start()
    
    # 根据模式显示不同的日志信息
    server_type = "远程" if default_mode == "remote" else "本地"
    actual_host = "0.0.0.0" if default_mode == "remote" and host == "localhost" else host
    logger.info(f"{server_type}Studio服务器已在后台启动: http://{actual_host}:{port}/studio")
    
    return server

def load_config(config_path: str = "config.json") -> Dict[str, Any]:
    """从配置文件加载配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    default_config = {
        "server": {
            "default_mode": "local",
            "host": "localhost",
            "port": 8000,
            "enable_studio": True
        },
        "cache": {
            "max_size": 256,
            "ttl_seconds": 3600,
            "enable_persistence": False
        },
        "memory": {
            "enable_memory": True,
            "db_path": "memory.db",
            "max_history_per_session": 50,
            "max_sessions": 100
        },
        "optimization": {
            "max_text_length": 200,
            "enable_deduplication": True,
            "batch_size": 10
        }
    }
    
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"从 {config_path} 加载配置")
                
                # 合并配置，确保所有必要的字段都存在
                for section in default_config:
                    if section not in config:
                        config[section] = default_config[section]
                    else:
                        for key in default_config[section]:
                            if key not in config[section]:
                                config[section][key] = default_config[section][key]
                
                return config
        else:
            logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
            return default_config
    except Exception as e:
        logger.error(f"加载配置文件时出错: {str(e)}，使用默认配置")
        return default_config

if __name__ == "__main__":
    import argparse
    
    # 加载配置文件
    config = load_config()
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="MCP收集器服务器")
    parser.add_argument("--config", default="config.json",
                        help="配置文件路径 (默认: config.json)")
    parser.add_argument("--mode", choices=["local", "remote"], 
                        default=config["server"]["default_mode"],
                        help=f"运行模式: local(本地) 或 remote(远程) (默认: {config['server']['default_mode']})")
    parser.add_argument("--host", default=config["server"]["host"],
                        help=f"服务器主机名 (默认: {config['server']['host']})")
    parser.add_argument("--port", type=int, default=config["server"]["port"],
                        help=f"服务器端口 (默认: {config['server']['port']})")
    parser.add_argument("--transport", choices=["stdio", "sse", "both"], default=None,
                        help="传输方式: stdio, sse 或 both (默认: 根据mode自动选择)")
    parser.add_argument("--studio", action="store_true", default=config["server"]["enable_studio"],
                        help="启动Studio界面")
    parser.add_argument("--cache-size", type=int, default=config["cache"]["max_size"],
                        help=f"缓存大小 (默认: {config['cache']['max_size']})")
    parser.add_argument("--memory", action="store_true", default=config["memory"]["enable_memory"],
                        help="启用记忆功能")
    parser.add_argument("--db-path", default=config["memory"]["db_path"],
                        help=f"记忆数据库路径 (默认: {config['memory']['db_path']})")
    
    args = parser.parse_args()
    
    # 如果指定了不同的配置文件，重新加载
    if args.config != "config.json":
        config = load_config(args.config)
    
    # 创建服务器实例
    server = create_server(
        cache_size=args.cache_size,
        enable_memory=args.memory,
        db_path=args.db_path,
        default_mode=args.mode,
        host=args.host,
        port=args.port
    )
    
    # 根据参数决定启动方式
    if args.studio:
        logger.info(f"以{args.mode}模式启动Studio服务器")
        server.run_studio()
    else:
        logger.info(f"以{args.mode}模式启动MCP服务器")
        server.run(transport=args.transport)