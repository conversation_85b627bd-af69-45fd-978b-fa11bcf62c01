<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Studio - 对话记忆管理</title>
    <style>
        :root {
            --primary-color: #4a6bdf;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-color: #dee2e6;
            --border-radius: 4px;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fb;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 300px 1fr;
            grid-gap: 20px;
            height: calc(100vh - 40px);
        }
        
        .sidebar {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .main-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .chat-container {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-messages {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .chat-input {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
        }
        
        .chat-input textarea {
            flex-grow: 1;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 10px;
            resize: none;
            height: 60px;
            margin-right: 10px;
        }
        
        .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: var(--border-radius);
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
            cursor: pointer;
        }
        
        .btn-primary {
            color: #fff;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #3a59c7;
            border-color: #3a59c7;
        }
        
        .btn-secondary {
            color: #fff;
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #5a6268;
        }
        
        .btn-danger {
            color: #fff;
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        
        .session-list {
            flex-grow: 1;
            overflow-y: auto;
            margin-bottom: 15px;
        }
        
        .session-item {
            padding: 10px 15px;
            border-radius: var(--border-radius);
            margin-bottom: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .session-item:hover {
            background-color: #f0f2f5;
        }
        
        .session-item.active {
            background-color: #e6f0ff;
            border-left: 3px solid var(--primary-color);
        }
        
        .session-item .session-name {
            font-weight: 500;
            margin-bottom: 3px;
        }
        
        .session-item .session-date {
            font-size: 0.8rem;
            color: var(--secondary-color);
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.assistant {
            justify-content: flex-start;
        }
        
        .message.system {
            justify-content: center;
        }
        
        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 18px;
            position: relative;
        }
        
        .user .message-content {
            background-color: var(--primary-color);
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .assistant .message-content {
            background-color: #f0f2f5;
            color: #333;
            border-bottom-left-radius: 4px;
        }
        
        .system .message-content {
            background-color: #ffeeba;
            color: #856404;
            font-style: italic;
            max-width: 90%;
            text-align: center;
            border-radius: 8px;
        }
        
        .message-time {
            font-size: 0.7rem;
            color: var(--secondary-color);
            margin-top: 5px;
            text-align: right;
        }
        
        .assistant .message-time {
            text-align: left;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 15px;
            color: var(--dark-color);
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--secondary-color);
            text-align: center;
            padding: 20px;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .empty-state p {
            margin-bottom: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .status-indicator.online {
            background-color: var(--success-color);
        }
        
        .status-indicator.offline {
            background-color: var(--danger-color);
        }
        
        .connection-status {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
        }
        
        .actions {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="section-title">对话会话</div>
            <div class="session-list" id="sessionList">
                <!-- 会话列表将通过JavaScript动态生成 -->
            </div>
            <button class="btn btn-primary" id="newSessionBtn">新建会话</button>
            <div class="connection-status" id="connectionStatus">
                <span class="status-indicator offline"></span>
                <span>未连接</span>
            </div>
        </div>
        
        <div class="main-content">
            <div class="chat-container">
                <div class="chat-header">
                    <h2 id="currentSessionName">未选择会话</h2>
                    <div class="actions">
                        <button class="btn btn-secondary" id="refreshBtn">刷新</button>
                        <button class="btn btn-danger" id="deleteSessionBtn" disabled>删除会话</button>
                    </div>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="empty-state">
                        <i>💬</i>
                        <h3>没有选择会话</h3>
                        <p>请从左侧选择一个会话，或者创建一个新的会话开始对话。</p>
                        <button class="btn btn-primary" id="emptyStateNewBtn">新建会话</button>
                    </div>
                </div>
                
                <div class="chat-input">
                    <textarea id="messageInput" placeholder="输入消息..." disabled></textarea>
                    <button class="btn btn-primary" id="sendBtn" disabled>发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 客户端ID，用于标识当前用户
        const clientId = 'client_' + Math.random().toString(36).substring(2, 15);
        
        // 当前选中的会话ID
        let currentSessionId = null;
        
        // SSE连接
        let eventSource = null;
        
        // DOM元素
        const sessionList = document.getElementById('sessionList');
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const newSessionBtn = document.getElementById('newSessionBtn');
        const emptyStateNewBtn = document.getElementById('emptyStateNewBtn');
        const deleteSessionBtn = document.getElementById('deleteSessionBtn');
        const refreshBtn = document.getElementById('refreshBtn');
        const currentSessionName = document.getElementById('currentSessionName');
        const connectionStatus = document.getElementById('connectionStatus');
        
        // 初始化
        function init() {
            // 连接SSE
            connectSSE();
            
            // 绑定事件
            newSessionBtn.addEventListener('click', createNewSession);
            emptyStateNewBtn.addEventListener('click', createNewSession);
            sendBtn.addEventListener('click', sendMessage);
            deleteSessionBtn.addEventListener('click', deleteCurrentSession);
            refreshBtn.addEventListener('click', refreshSessions);
            
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // 加载会话列表
            refreshSessions();
        }
        
        // 连接SSE
        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            eventSource = new EventSource(`/events?client_id=${clientId}`);
            
            eventSource.onopen = () => {
                updateConnectionStatus(true);
            };
            
            eventSource.onerror = () => {
                updateConnectionStatus(false);
                // 尝试重连
                setTimeout(connectSSE, 5000);
            };
            
            eventSource.onmessage = (event) => {
                const data = JSON.parse(event.data);
                handleServerEvent(data);
            };
        }
        
        // 更新连接状态
        function updateConnectionStatus(isConnected) {
            const indicator = connectionStatus.querySelector('.status-indicator');
            const text = connectionStatus.querySelector('span:last-child');
            
            if (isConnected) {
                indicator.className = 'status-indicator online';
                text.textContent = '已连接';
            } else {
                indicator.className = 'status-indicator offline';
                text.textContent = '未连接';
            }
        }
        
        // 处理服务器事件
        function handleServerEvent(data) {
            if (data.event === 'connected') {
                console.log('Connected to server with client ID:', data.client_id);
            } else if (data.event === 'ping') {
                // 心跳包，保持连接
            } else if (data.event === 'session_updated') {
                refreshSessions();
            } else if (data.event === 'message_added') {
                if (data.data.session_id === currentSessionId) {
                    loadMessages(currentSessionId);
                }
            }
        }
        
        // 刷新会话列表
        function refreshSessions() {
            fetch('/api/sessions')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        renderSessionList(data.sessions);
                    } else {
                        console.error('Failed to load sessions:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading sessions:', error);
                });
        }
        
        // 渲染会话列表
        function renderSessionList(sessions) {
            sessionList.innerHTML = '';
            
            if (sessions.length === 0) {
                const emptyEl = document.createElement('div');
                emptyEl.className = 'empty-state';
                emptyEl.innerHTML = '<p>没有会话记录</p>';
                sessionList.appendChild(emptyEl);
                return;
            }
            
            sessions.forEach(session => {
                const sessionEl = document.createElement('div');
                sessionEl.className = 'session-item';
                if (session.id === currentSessionId) {
                    sessionEl.classList.add('active');
                }
                
                const date = new Date(session.updated_at * 1000);
                const dateStr = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
                
                sessionEl.innerHTML = `
                    <div class="session-name">${session.name || '未命名会话'}</div>
                    <div class="session-date">${dateStr}</div>
                `;
                
                sessionEl.addEventListener('click', () => {
                    selectSession(session.id);
                });
                
                sessionList.appendChild(sessionEl);
            });
        }
        
        // 选择会话
        function selectSession(sessionId) {
            currentSessionId = sessionId;
            
            // 更新UI
            const sessionItems = sessionList.querySelectorAll('.session-item');
            sessionItems.forEach(item => {
                item.classList.remove('active');
                if (item.querySelector('.session-name').textContent === sessionId) {
                    item.classList.add('active');
                }
            });
            
            // 启用输入
            messageInput.disabled = false;
            sendBtn.disabled = false;
            deleteSessionBtn.disabled = false;
            
            // 加载会话信息
            fetch(`/api/sessions/${sessionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        currentSessionName.textContent = data.session.name || '未命名会话';
                    }
                });
            
            // 加载消息
            loadMessages(sessionId);
        }
        
        // 加载消息
        function loadMessages(sessionId) {
            fetch(`/api/sessions/${sessionId}/messages`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        renderMessages(data.messages);
                    } else {
                        console.error('Failed to load messages:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                });
        }
        
        // 渲染消息
        function renderMessages(messages) {
            chatMessages.innerHTML = '';
            
            if (messages.length === 0) {
                const emptyEl = document.createElement('div');
                emptyEl.className = 'empty-state';
                emptyEl.innerHTML = `
                    <i>💬</i>
                    <h3>没有消息</h3>
                    <p>开始发送消息，与AI助手对话。</p>
                `;
                chatMessages.appendChild(emptyEl);
                return;
            }
            
            messages.forEach(message => {
                const messageEl = document.createElement('div');
                messageEl.className = `message ${message.role}`;
                
                const date = new Date(message.timestamp * 1000);
                const timeStr = date.toLocaleTimeString();
                
                messageEl.innerHTML = `
                    <div class="message-content">
                        ${message.content}
                        <div class="message-time">${timeStr}</div>
                    </div>
                `;
                
                chatMessages.appendChild(messageEl);
            });
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 创建新会话
        function createNewSession() {
            const sessionName = prompt('请输入会话名称（可选）：');
            
            fetch('/api/sessions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: sessionName || '',
                    client_id: clientId
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        refreshSessions();
                        selectSession(data.session.id);
                    } else {
                        console.error('Failed to create session:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error creating session:', error);
                });
        }
        
        // 发送消息
        function sendMessage() {
            const content = messageInput.value.trim();
            if (!content || !currentSessionId) return;
            
            // 清空输入框
            messageInput.value = '';
            
            // 添加用户消息到UI
            addMessageToUI('user', content);
            
            // 发送到服务器
            fetch('/api/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: content,
                    client_id: clientId,
                    remember: true,
                    session_id: currentSessionId
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.result) {
                        // 添加助手回复到UI
                        addMessageToUI('assistant', data.result);
                    } else {
                        console.error('Failed to process message:', data);
                    }
                })
                .catch(error => {
                    console.error('Error sending message:', error);
                });
        }
        
        // 添加消息到UI
        function addMessageToUI(role, content) {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${role}`;
            
            const date = new Date();
            const timeStr = date.toLocaleTimeString();
            
            messageEl.innerHTML = `
                <div class="message-content">
                    ${content}
                    <div class="message-time">${timeStr}</div>
                </div>
            `;
            
            chatMessages.appendChild(messageEl);
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 删除当前会话
        function deleteCurrentSession() {
            if (!currentSessionId) return;
            
            if (!confirm('确定要删除这个会话吗？此操作不可撤销。')) {
                return;
            }
            
            fetch(`/api/sessions/${currentSessionId}`, {
                method: 'DELETE'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        currentSessionId = null;
                        currentSessionName.textContent = '未选择会话';
                        messageInput.disabled = true;
                        sendBtn.disabled = true;
                        deleteSessionBtn.disabled = true;
                        
                        chatMessages.innerHTML = `
                            <div class="empty-state">
                                <i>💬</i>
                                <h3>没有选择会话</h3>
                                <p>请从左侧选择一个会话，或者创建一个新的会话开始对话。</p>
                                <button class="btn btn-primary" id="emptyStateNewBtn">新建会话</button>
                            </div>
                        `;
                        
                        document.getElementById('emptyStateNewBtn').addEventListener('click', createNewSession);
                        
                        refreshSessions();
                    } else {
                        console.error('Failed to delete session:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error deleting session:', error);
                });
        }
        
        // 初始化应用
        init();
    </script>
</body>
</html>