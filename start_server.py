#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP收集器服务器启动脚本
提供简单的命令行界面，让用户选择本地或远程模式
"""

import os
import sys
import json
import argparse
from optimized_server import create_server, load_config

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="MCP收集器服务器启动脚本")
    parser.add_argument("--config", default="config.json",
                        help="配置文件路径 (默认: config.json)")
    parser.add_argument("--quick-start", action="store_true",
                        help="快速启动，使用配置文件中的设置")
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 如果是快速启动，直接使用配置文件中的设置
    if args.quick_start:
        mode = config["server"]["default_mode"]
        host = config["server"]["host"]
        port = config["server"]["port"]
        enable_studio = config["server"]["enable_studio"]
    else:
        # 交互式选择
        print("=" * 50)
        print("MCP收集器服务器启动脚本")
        print("=" * 50)
        
        # 选择模式
        print("\n请选择运行模式:")
        print("1. 本地模式 (localhost)")
        print("2. 远程模式 (0.0.0.0，允许远程连接)")
        
        while True:
            choice = input("\n请输入选项 [1/2] (默认: {}): ".format(
                "1" if config["server"]["default_mode"] == "local" else "2"
            )).strip()
            
            if not choice:
                mode = config["server"]["default_mode"]
                break
            
            if choice == "1":
                mode = "local"
                break
            elif choice == "2":
                mode = "remote"
                break
            else:
                print("无效的选项，请重新输入")
        
        # 选择端口
        while True:
            port_input = input(f"\n请输入端口号 (默认: {config['server']['port']}): ").strip()
            
            if not port_input:
                port = config["server"]["port"]
                break
            
            try:
                port = int(port_input)
                if 1024 <= port <= 65535:
                    break
                else:
                    print("端口号必须在1024-65535之间")
            except ValueError:
                print("请输入有效的端口号")
        
        # 选择是否启用Studio界面
        while True:
            studio_choice = input("\n是否启用Studio界面? [y/n] (默认: {}): ".format(
                "y" if config["server"]["enable_studio"] else "n"
            )).strip().lower()
            
            if not studio_choice:
                enable_studio = config["server"]["enable_studio"]
                break
            
            if studio_choice in ["y", "yes"]:
                enable_studio = True
                break
            elif studio_choice in ["n", "no"]:
                enable_studio = False
                break
            else:
                print("无效的选项，请重新输入")
        
        # 设置主机名
        host = "localhost" if mode == "local" else "0.0.0.0"
        
        # 询问是否保存设置为默认值
        save_choice = input("\n是否将当前设置保存为默认值? [y/n] (默认: n): ").strip().lower()
        
        if save_choice in ["y", "yes"]:
            config["server"]["default_mode"] = mode
            config["server"]["host"] = host
            config["server"]["port"] = port
            config["server"]["enable_studio"] = enable_studio
            
            try:
                with open(args.config, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)
                print(f"\n设置已保存到 {args.config}")
            except Exception as e:
                print(f"\n保存设置时出错: {str(e)}")
    
    # 创建并启动服务器
    print("\n" + "=" * 50)
    print(f"启动MCP收集器服务器 - {'本地' if mode == 'local' else '远程'}模式")
    print(f"主机: {host}")
    print(f"端口: {port}")
    print(f"Studio界面: {'启用' if enable_studio else '禁用'}")
    print("=" * 50 + "\n")
    
    server = create_server(
        cache_size=config["cache"]["max_size"],
        enable_memory=config["memory"]["enable_memory"],
        db_path=config["memory"]["db_path"],
        default_mode=mode,
        host=host,
        port=port
    )
    
    if enable_studio:
        server.run_studio(host=host, port=port)
    else:
        transport = "stdio" if mode == "local" else "sse"
        server.run(transport=transport, host=host, port=port)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        sys.exit(0)